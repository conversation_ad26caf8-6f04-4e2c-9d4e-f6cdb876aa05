body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}
.month-view {
    display: none;
}

.width-300 {
    width: 300px;
}

.min-width-120 {
    min-width: 120px;
}


.wintable td,
.wintable th {
  padding: .5rem;
  min-width: 130px;
}

.font-small {
    font-size: small;
}

.font-smaller {
    font-size: smaller;
}

.font-medium {
    font-size: medium;
}

.font-x-large {
    font-size: x-large;
}

.font-xxx-large {
    font-size: xxx-large;
}

.custom-date-range {
    width: 100%;
    min-height: calc(1.5em + .75rem + 5px);
    padding: .375rem .375rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

.att-info-box {
    position: relative;
    padding-bottom: 15px !important;
}

.info-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    margin: 5px;
    background-color: #fff;
    border-radius: 50%;
}
.day-of-month{
    float: right;
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.monthly-popup .table td {
    vertical-align: top !important;
    width: 133px;
    min-height: 113px;
}

.monthly-popup .att-info-box {
  position: unset;
  margin-top: 20px !important;
  display: inline-block;
  min-width: 100%;
  min-height: 90%;
}
.monthly-popup  .date-box{
    width: 100%;
}
.monthly-popup .date-label{
    right: 0px;
    top: 0px;
}
.outer .inner .wintable{
    width: 100% !important;
  }

  .outer .inner  .fix {
    position: absolute !important;
    width: 20vw;
    min-height: 93px;
    border: none;
    margin-left: -20vw;
    padding: 1px 2.5px;
  }



  .inner {
    overflow-x: scroll;
    overflow-y: auto;
    width: 100%;
    max-height: 70vh; /* Set a max height to enable vertical scrolling */
  }

  .cell-min-height{
    min-height: 96px;
  }

  /* Sticky header for vertical scrolling */
  .outer .inner .wintable thead {
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .outer .inner .wintable thead th {
    background: #6c757d !important; /* Maintain header background color */
    position: sticky;
    top: 0;
  }

  /* Ensure the fixed left column header stays on top and maintains original styling */
  .outer .inner .wintable thead th.fix {
    z-index: 11;
    background: #0169ab !important;
    /* Ensure the legends/checkboxes are visible and properly sized */
    min-height: 93px;
    vertical-align: top;
    padding: 8px;
  }

  /* Ensure patient names fit properly in the layout */
  .day-body-col.fix {
    border-top: 1px solid #dee2e6 !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  /* Improve text wrapping for patient information */
  .day-body-col.fix .btn-link {
    white-space: normal;
    text-align: left;
    word-break: break-word;
  }

  /* Ensure legends/checkboxes in header are properly spaced and visible */
  .outer .inner .wintable thead th.fix .form-check {
    margin-bottom: 4px;
  }

  .outer .inner .wintable thead th.fix .form-check-label {
    font-size: 11px;
    line-height: 1.2;
    margin-left: 2px;
  }

  .outer .inner .wintable thead th.fix .row {
    margin: 4px 2px;
  }

  /* Ensure patient information fits properly */
  .day-body-col.fix .font-small {
    font-size: 10px;
    line-height: 1.3;
  }

  .day-body-col.fix .font-medium {
    font-size: 12px;
    line-height: 1.3;
  }


@media (min-width: 577px) {
    .container {
        width: 100%;
        max-width: unset;
        padding: 0 30px;
    }
    .outer {
        margin-left: 20vw;
        position: relative;
        background-color: #fff;
      }
}

@media (max-width: 576px) {
    .min-width-80 {
        min-width: 80px;
    }

    .filter-icon-wrapper {
        fill: white;
    }

    .container {
        padding: 0;
    }

    .carousel-inner {
        position: relative;
        overflow: hidden;
    }

    .carousel-item {
        width: 90%;
        margin: auto;
        text-align: center;
        float: none;
    }

    .carousel-control-prev,
    .carousel-control-next {
        width: 5%;
        opacity: 1;
        background: transparent;
    }

    .carousel-control-prev-icon {
        background-image: url(../../../assets/left-arrow-next.svg);
    }

    .carousel-control-next-icon {
        background-image: url(../../../assets/right-arrow-next.svg);
    }

    #myCarousel {
        overflow: hidden;
    }

    .card-head,
    .card-body {
        border: 1px solid #fff;
    }

    .close-button {
        float: right;
        margin-top: 0px;
        margin-right: 10px;
    }

    .close-button img {
        width: 15px;
    }  
    
}
