<div class="row mx-0">
    <div class="col-12 px-0">
        <!-- table start -->
        <div *ngIf="!device">
            <table class="table mb-0 customDataTable width-100per" border="1">
                <thead>
                    <tr>
                        <th class="text-center pb-0 cursor-pointer"
                        *ngIf="userAccess.physicianModuleAccess=='YES' && pageName=='/physician/pending-approval-encounters'">
                            <input type="checkbox"  [attr.disabled]="listOfPatients.length==0 ? true : null" id="selectAll" [(ngModel)]="isSelectAllChecked" (change)="chkAllForApprove($event)">
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('account_Number')"
                            (click)="sortColumn('account_Number')">
                            Account No#
                            <span class="float-right" tooltip="Sort By Account#"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'account_Number' && orderByAccount == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'account_Number' && orderByAccount == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'account_Number'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('patient_Name')"
                            (click)="sortColumn('patient_Name')">
                            Patient Name
                            <span class="float-right" tooltip="Sort By Patient"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'patient_Name' && orderByPatient == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'patient_Name' && orderByPatient == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'patient_Name'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('facility_Name')"
                            (click)="sortColumn('facility_Name')">
                            Facility
                            <span class="float-right" tooltip="Sort By Facility"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'facility_Name' && orderByFacility == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'facility_Name' && orderByFacility == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'facility_Name'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer" (keyup)="sortColumn('age')"
                            (click)="sortColumn('age')">
                            Age
                            <span class="float-right" tooltip="Sort By Age"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'age' && orderByAge == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'age' && orderByAge == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'age'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('room_Number')" (click)="sortColumn('room_Number')">
                            Room No
                            <span class="float-right" tooltip="Sort By Room"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'room_Number' && orderByRoom == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'room_Number' && orderByRoom == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'room_Number'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('admission_Type')"
                            (click)="sortColumn('admission_Type')">
                            Patient Type
                            <span class="float-right" tooltip="Sort By Patient Type"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'admission_Type' && orderByPtType == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'admission_Type' && orderByPtType == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'admission_Type'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('attending_Physician_InApp')"
                            (click)="sortColumn('attending_Physician_InApp')">
                            Attending Provider
                            <span class="float-right" tooltip="Sort By Attending Provider"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'attending_Physician_InApp' && attendingPhysician == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'attending_Physician_InApp' && attendingPhysician == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'attending_Physician_InApp'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('arithmetic_Mean_LOS')"
                            (click)="sortColumn('arithmetic_Mean_LOS')">
                            LOS
                            <span class="float-right" tooltip="Sort By LOS"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'arithmetic_Mean_LOS' && orderByLOS == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'arithmetic_Mean_LOS' && orderByLOS == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'arithmetic_Mean_LOS'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0 cursor-pointer"
                            (keyup)="sortColumn('reimbursement_Type')"
                            (click)="sortColumn('reimbursement_Type')">
                            Payer Class
                            <span class="float-right" tooltip="Sort By Payer Class"
                                triggers="mouseenter mouseleave click">
                                <i *ngIf="sortColumnBy == 'reimbursement_Type' && orderByPayer == 'desc'"
                                    class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy == 'reimbursement_Type' && orderByPayer == 'asc'"
                                    class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                <i *ngIf="sortColumnBy != 'reimbursement_Type'"
                                    class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                            </span>
                        </th>
                        <th class="text-center pb-0">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container
                        *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i = index;">
                        <tr *ngIf="item.account_Number!=''">
                            <td data-th="approve" class="text-left text-md-center pb-0"
                              *ngIf="userAccess.physicianModuleAccess=='YES' && pageName=='/physician/pending-approval-encounters'">
                                <input type="checkbox" id="{{item.encounteR_ID}}" [(ngModel)]="item.ischecked" (change)="chkHaveanyToApprove()">
                            </td>
                            <td data-th="Account No#" class="text-left text-md-center pb-0">
                                <span class="mobile-text"><button class="btn btn-link"
                                        [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                        placement="right" triggers="click" [outsideClick]="true">
                                        {{item.account_Number}}
                                    </button></span>
                                <ng-template #popTemplate>
                                    <div class="accordion-inner">
                                        <ul class="my-auto">
                                            <!-- Has Today Env -->
                                            <ng-container
                                                *ngIf="item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'">
                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        *ngIf="residentAccess=='YES';else resEls"
                                                        [routerLink]="['/physician/approve-pending-encounter']"
                                                        [state]="{patient:item,backUrl:pageName}">
                                                        Start New Encounter
                                                    </a>
                                                    <ng-template #resEls>
                                                        <a class="text-white"
                                                            [routerLink]="['/physician/start-new-encounter']"
                                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                            <span>Start New Encounter</span>
                                                        </a>
                                                    </ng-template>
                                                </li>
                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                                    *ngIf="item.isPostToBiller=='0'">
                                                    <a class="text-white"
                                                        (click)="UpdatePosttoBiller(item)">
                                                        Post To Biller</a>
                                                </li>
                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                                    *ngIf="item.isPostToBiller=='1'">
                                                    Already Post To Biller</li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                                    *ngIf="item.hasPriorHistory =='1'">
                                                    <a class="text-white"
                                                        (click)="viewHistory(item)">
                                                        View Past History</a>
                                                </li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                                    *ngIf="item.hasPriorHistory =='0'">No Prior
                                                    History</li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="openNotes(item)">Notes
                                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                                </li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="getAttachments(item)">Attachment
                                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                                </li>
                                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="openEditPatient(item)">Edit
                                                            Patient</a>
                                                    </li>
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="CloseHospitalizationStatus(item)">
                                                            Close Hospitalization</a>
                                                    </li>
                                                </ng-container>
                                            </ng-container>
                                            <!-- Has Draft Env -->
                                            <ng-container
                                                *ngIf="item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null">
                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1" *ngIf="pageName=='/physician/pending-approval-encounters';else penAppp">
                                                    <a class="text-white"
                                                        [routerLink]="['/physician/approve-pending-encounter']"
                                                        [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                        View/Start New Encounter
                                                    </a>
                                                </li>
                                                <ng-template #penAppp>
                                                    <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            *ngIf="residentAccess=='YES';else resEls"
                                                            [routerLink]="['/physician/approve-pending-encounter']"
                                                            [state]="{patient:item,backUrl:pageName}">
                                                            My Encounter
                                                        </a>
                                                        <ng-template #resEls>
                                                            <a class="text-white"
                                                                [routerLink]="['/physician/start-new-encounter']"
                                                                [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                                <span>My Encounter</span>
                                                            </a>
                                                        </ng-template>
                                                    </li>
                                                </ng-template>
                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="getAttachments(item)">Attachment
                                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                                </li>
                                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="openEditPatient(item)">Edit
                                                            Patient</a>
                                                    </li>
                                                </ng-container>
                                            </ng-container>
                                            <!-- Has Old Env -->
                                            <ng-container
                                                *ngIf="item.hasPreviousEncounter=='1' && (item.encounteR_ID==null || item.encounteR_ID =='0' ) && (item.resetPriorEncounterStatus==null || item.resetPriorEncounterStatus=='0' )">
                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        *ngIf="residentAccess=='YES';else resEls"
                                                        [routerLink]="['/physician/approve-pending-encounter']"
                                                        [state]="{patient:item,backUrl:pageName}">
                                                        Start New Encounter
                                                    </a>
                                                    <ng-template #resEls>
                                                        <a class="text-white"
                                                            [routerLink]="['/physician/start-new-encounter']"
                                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                            <span>Start New Encounter</span>
                                                        </a>
                                                    </ng-template>
                                                </li>
                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                                    *ngIf="item.isPostToBiller=='0'">
                                                    <a class="text-white"
                                                        (click)="UpdatePosttoBiller(item)">
                                                        Post To Biller</a>
                                                </li>
                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                                    *ngIf="item.isPostToBiller=='1'">
                                                    Already Post To Biller</li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                                    *ngIf="item.hasPriorHistory =='1'">
                                                    <a class="text-white"
                                                        (click)="viewHistory(item)">
                                                        View Past History</a>
                                                </li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                                    *ngIf="item.hasPriorHistory =='0'">No Prior
                                                    History</li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="openNotes(item)">Notes
                                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                                </li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="getAttachments(item)">Attachment
                                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                                </li>
                                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="openEditPatient(item)">Edit
                                                            Patient</a>
                                                    </li>
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="CloseHospitalizationStatus(item)">
                                                            Close Hospitalization</a>
                                                    </li>
                                                </ng-container>
                                            </ng-container>
                                            <!-- Has No Env -->
                                            <ng-container *ngIf="item.hasPreviousEncounter=='0'">
                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        *ngIf="residentAccess=='YES';else resEls"
                                                        [routerLink]="['/physician/approve-pending-encounter']"
                                                        [state]="{patient:item,backUrl:pageName}">
                                                        Start New Encounter
                                                    </a>
                                                    <ng-template #resEls>
                                                        <a class="text-white"
                                                            [routerLink]="['/physician/start-new-encounter']"
                                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                            <span>Start New Encounter</span>
                                                        </a>
                                                    </ng-template>
                                                </li>
                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color">
                                                    No Encounter to Post To Biller</li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                                    *ngIf="item.hasPriorHistory =='1'">
                                                    <a class="text-white"
                                                        (click)="viewHistory(item)">
                                                        View Past History</a>
                                                </li>

                                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                                    *ngIf="item.hasPriorHistory =='0'">No Prior
                                                    History</li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="openNotes(item)">Notes
                                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                                </li>

                                                <li
                                                    class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                    <a class="text-white"
                                                        (click)="getAttachments(item)">Attachment
                                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                                </li>
                                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="openEditPatient(item)">Edit
                                                            Patient</a>
                                                    </li>
                                                    <li
                                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                                        <a class="text-white"
                                                            (click)="CloseHospitalizationStatus(item)">
                                                            Close Hospitalization</a>
                                                    </li>
                                                </ng-container>
                                            </ng-container>
                                        </ul>
                                    </div>
                                </ng-template>

                            </td>
                            <td data-th="Patient Name" class="text-left text-md-center pb-0">

                                <button class="btn btn-link font-weight-normal"
                                    [ngClass]="{'hasTodayEncounter':(item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),'hasDraftEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),'hasPriorEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),'hasNoEncounter':(item.hasPreviousEncounter=='0')}"
                                    [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                    placement="right" triggers="click" [outsideClick]="true">
                                    {{item.patient_Name}}
                                </button>
                            </td>
                            <td data-th="Facility" class="text-left text-md-center pb-0"><span
                                    class="mobile-text">{{item.facility_Name}}</span></td>
                            <td data-th="Age" class="text-left text-md-center pb-0"><span
                                    class="mobile-text">{{item.age}}</span></td>
                            <td data-th="Room No" class="text-left text-md-center pb-0"><span
                                    class="mobile-text">{{item.room_Number}}</span></td>
                            <td data-th="Patient Type" class="text-left text-md-center pb-0"><span
                                    class="mobile-text">{{item.admission_Type}}</span></td>
                            <td data-th="Attending Provider" class="text-left text-md-center pb-0">
                                <span class="mobile-text">{{item.attending_Physician_InApp}}</span>
                            </td>
                            <td data-th="LOS" class="text-left text-md-center pb-0"
                                [style.color]="item.color"><span
                                    class="mobile-text">{{item.arithmetic_Mean_LOS}}</span></td>
                            <td data-th="Payer Class" class="text-left text-md-center pb-0"><span
                                    class="mobile-text">{{item.reimbursement_Type}}</span></td>
                            <td data-th="Actions" class="text-left text-md-center pb-0">
                                <span class="mobile-text">
                                    <a *ngIf="pageName=='/physician/my-group-patients' || pageName=='/physician/my-patients'" class="mx-1" title="Remove patient" (click)='RemovePatient(item)'>
                                        <i class="far fa-trash-alt text-danger"></i>
                                    </a>
                                    <a class="mx-1" *ngIf="(pageName=='/physician/my-group-patients' || pageName=='/physician/hospital-census') && !item.attending_Physician_InApp.includes(userName)"
                                        title="Assign to my list" (click)='assignToMyListSave(item)'>
                                        <i class="fas fa-user text-info"></i>
                                    </a>
                                    <span *ngIf="pageName=='/physician/my-patients'">
                                        <a class="mx-1" *ngIf='item.isHide==0' title="Hide" (click)='HidePatient(item)'><i
                                                class="far fa-eye-slash text-dark"></i></a>
                                        <a class="mx-1" *ngIf='item.isHide==1' title="Un-Hide" (click)='UnHidePatient(item)'><i
                                                class="far fa-eye text-dark"></i></a>
                                    </span>
                                    <a *ngIf="pageName=='/physician/discharge-patients'" class="mx-1" title="Un-Discharge" (click)='openUnDischargePatientPop(item)'><i
                                            class="fas fa-user-alt-slash text-info"></i>
                                    </a>
                                    <a class="mx-1" title="Assign to others"
                                        (click)='assignToOthersPopup(item)'><i
                                            class="fas fa-user-tag text-info"></i></a>
                                    <a class="mx-1" *ngIf='item.isResidentAcc==1 || item.isResidents==1'
                                        (click)='assignToResidentPopup(item)'
                                        title="Assign to Resident"><i
                                            class="fas fa-home text-primary"></i></a>
                                </span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="11" class="m-0 p-0" style="background: white !important;">
                            <pagination-controls previousLabel="" nextLabel=""
                                (pageChange)="getPatients($event)">
                            </pagination-controls>
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <!-- table end -->
        <!-- mobile start-->
        <div *ngIf="device">
            <ng-container
                *ngFor="let item of listOfPatients | paginate: { itemsPerPage: 14, currentPage: p,totalItems:totalCount}; let i = index;">
                <div
                    class="row border-blue bg-white my-1 shadow border mx-auto mobile text-secondary">
                    <div class="col p-1 font-weight-bold cursor-pointer small"
                        [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                        placement="right" triggers="click" [outsideClick]="true">
                        <h5 class="h5 my-auto"
                            [ngClass]="{'hasTodayEncounter':(item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),'hasDraftEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),'hasPriorEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),'hasNoEncounter':(item.hasPreviousEncounter=='0')}">
                            {{item.patient_Name}}</h5>
                        <div class="d-inline-block pb-1 pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">
                                <span>{{item.account_Number}}</span>
                            </h6>
                        </div>
                        <div class="d-inline-block pb-1 pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">
                                <span
                                    class="bg-blue text-white px-1 rounded border">{{item.facility_Name}}</span>
                            </h6>
                        </div>

                        <div class="d-inline-block pb-1 pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto border rounded-sm px-1"
                                [style.border-color]="item.color">
                                <span [style.color]="item.color">{{item.arithmetic_Mean_LOS}}</span>
                            </h6>
                        </div>
                    </div>
                    <div class="col-5 my-auto p-1 small border border-blue border-top-0 border-bottom-0 cursor-pointer"
                        [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                        placement="left" triggers="click" [outsideClick]="true">

                        <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">Age: <span>{{item.age}}</span></h6>
                        </div>

                        <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">Room:
                                <span>{{item.room_Number}}</span></h6>
                        </div>
                        <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">Type:
                                <span>{{item.admission_Type}}</span></h6>
                        </div>

                        <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto">
                                Payer: <span>{{item.reimbursement_Type}}</span>
                            </h6>
                        </div>

                        <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                            <h6 class="card-title small my-auto border bg-light rounded-sm p-1">
                                <span>{{item.attending_Physician_InApp}}</span>
                            </h6>
                        </div>

                    </div>
                    <div class="col h6 my-auto p-1 font-weight-bold">
                        <div class="row mx-auto">
                            <div class="col px-0 mx-auto text-center">
                                <div class="p-0">
                                    <a *ngIf="pageName=='/physician/my-group-patients' || pageName=='/physician/my-patients'" class="mx-1"
                                        title="Remove patient" (click)='RemovePatient(item)'>
                                        <i class="far fa-trash-alt text-danger"></i>
                                    </a>
                                    <a class="mx-1"
                                        *ngIf="(pageName=='/physician/my-group-patients' || pageName=='/physician/hospital-census') && !item.attending_Physician_InApp.includes(userName)"
                                        title="Assign to my list" (click)='assignToMyListSave(item)'>
                                        <i class="fas fa-user text-info"></i>
                                    </a>
                                    <span *ngIf="pageName=='/physician/my-patients'">
                                        <a class="mx-1" *ngIf='item.isHide==0' title="Hide" (click)='HidePatient(item)'><i
                                                class="far fa-eye-slash text-dark"></i></a>
                                        <a class="mx-1" *ngIf='item.isHide==1' title="Un-Hide" (click)='UnHidePatient(item)'><i
                                                class="far fa-eye text-dark"></i></a>
                                    </span>
                                    <a *ngIf="pageName=='/physician/discharge-patients'" class="mx-1" title="Un-Discharge"
                                        (click)='openUnDischargePatientPop(item)'><i class="fas fa-user-alt-slash text-info"></i>
                                    </a>
                                    <a class="mx-1" title="Assign to others" (click)='assignToOthersPopup(item)'><i
                                            class="fas fa-user-tag text-info"></i></a>
                                    <a class="mx-1" *ngIf='item.isResidentAcc==1 || item.isResidents==1' (click)='assignToResidentPopup(item)'
                                        title="Assign to Resident"><i class="fas fa-home text-primary"></i></a>
                                    <a class="mx-1" title="Approve Encounter" (click)='approveEncounterMyMobile(item)'
                                        *ngIf="userAccess.physicianModuleAccess=='YES' && pageName=='/physician/pending-approval-encounters'">
                                        <i class="fas fa-check text-primary"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <ng-template #popTemplate>
                    <div class="accordion-inner">
                        <ul class="my-auto">
                            <!-- Has Today Env -->
                            <ng-container
                                *ngIf="item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'">
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" *ngIf="residentAccess=='YES';else resEls"
                                        [routerLink]="['/physician/approve-pending-encounter']"
                                        [state]="{patient:item,backUrl:pageName}">
                                        Start New Encounter
                                    </a>
                                    <ng-template #resEls>
                                        <a class="text-white"
                                            [routerLink]="['/physician/start-new-encounter']"
                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                            <span>Start New Encounter</span>
                                        </a>
                                    </ng-template>
                                </li>
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="item.isPostToBiller=='0'">
                                    <a class="text-white" (click)="UpdatePosttoBiller(item)">
                                        Post To Biller</a>
                                </li>
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                    *ngIf="item.isPostToBiller=='1'">
                                    Already Post To Biller</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="item.hasPriorHistory =='1'">
                                    <a class="text-white" (click)="viewHistory(item)">
                                        View Past History</a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                    *ngIf="item.hasPriorHistory =='0'">No Prior History</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="openNotes(item)">Notes
                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="getAttachments(item)">Attachment
                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                </li>
                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white" (click)="openEditPatient(item)">Edit
                                            Patient</a>
                                    </li>
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white"
                                            (click)="CloseHospitalizationStatus(item)">
                                            Close Hospitalization</a>
                                    </li>
                                </ng-container>
                            </ng-container>
                            <!-- Has Draft Env -->
                            <ng-container
                                *ngIf="item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null">
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="pageName=='/physician/pending-approval-encounters';else penAppp">
                                    <a class="text-white" [routerLink]="['/physician/approve-pending-encounter']"
                                    [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                        View/Start New Encounter
                                    </a>
                                </li>
                                <ng-template #penAppp>
                                    <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white" *ngIf="residentAccess=='YES';else resEls"
                                            [routerLink]="['/physician/approve-pending-encounter']" [state]="{patient:item,backUrl:pageName}">
                                            My Encounter
                                        </a>
                                        <ng-template #resEls>
                                            <a class="text-white" [routerLink]="['/physician/start-new-encounter']"
                                                [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                                <span>My Encounter</span>
                                            </a>
                                        </ng-template>
                                    </li>
                                </ng-template>
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="getAttachments(item)">Attachment
                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                </li>
                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white" (click)="openEditPatient(item)">Edit
                                            Patient</a>
                                    </li>
                                </ng-container>
                            </ng-container>
                            <!-- Has Old Env -->
                            <ng-container
                                *ngIf="item.hasPreviousEncounter=='1' && (item.encounteR_ID==null || item.encounteR_ID=='0') && (item.resetPriorEncounterStatus==null || item.resetPriorEncounterStatus=='0') ">
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" *ngIf="residentAccess=='YES';else resEls"
                                        [routerLink]="['/physician/approve-pending-encounter']"
                                        [state]="{patient:item,backUrl:pageName}">
                                        Start New Encounter
                                    </a>
                                    <ng-template #resEls>
                                        <a class="text-white"
                                            [routerLink]="['/physician/start-new-encounter']"
                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                            <span>Start New Encounter</span>
                                        </a>
                                    </ng-template>
                                </li>
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="item.isPostToBiller=='0'">
                                    <a class="text-white" (click)="UpdatePosttoBiller(item)">
                                        Post To Biller</a>
                                </li>
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                    *ngIf="item.isPostToBiller=='1'">
                                    Already Post To Biller</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="item.hasPriorHistory =='1'">
                                    <a class="text-white" (click)="viewHistory(item)">
                                        View Past History</a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                    *ngIf="item.hasPriorHistory =='0'">No Prior History</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="openNotes(item)">Notes
                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="getAttachments(item)">Attachment
                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                </li>
                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white" (click)="openEditPatient(item)">Edit
                                            Patient</a>
                                    </li>
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white"
                                            (click)="CloseHospitalizationStatus(item)">
                                            Close Hospitalization</a>
                                    </li>
                                </ng-container>
                            </ng-container>
                            <!-- Has No Env -->
                            <ng-container *ngIf="item.hasPreviousEncounter=='0'">
                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" *ngIf="residentAccess=='YES';else resEls"
                                        [routerLink]="['/physician/approve-pending-encounter']"
                                        [state]="{patient:item,backUrl:pageName}">
                                        Start New Encounter
                                    </a>
                                    <ng-template #resEls>
                                        <a class="text-white"
                                            [routerLink]="['/physician/start-new-encounter']"
                                            [state]="{patient:item,encounterSeenDate:encounterSeenDate,backUrl:pageName,facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchByName,ddlFacility:FilterForm.value.ddlFacility,ddlGroups:FilterForm.value.ddlGroups,ddlphybygroup:FilterForm.value.ddlphybygroup,ddlPatients:FilterForm.value.ddlPatients,ddlDepartment:FilterForm.value.ddlDepartment,txtFromDate:FilterForm.value.txtFromDate,txtToDate:FilterForm.value.txtToDate}}">
                                            <span>Start New Encounter</span>
                                        </a>
                                    </ng-template>
                                </li>
                                <li
                                    class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color">
                                    No Encounter to Post To Biller</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1"
                                    *ngIf="item.hasPriorHistory =='1'">
                                    <a class="text-white" (click)="viewHistory(item)">
                                        View Past History</a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1 no_link_color"
                                    *ngIf="item.hasPriorHistory =='0'">No Prior History</li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="openNotes(item)">Notes
                                        <span>(</span><span>{{item.notesCount}}</span><span>)</span></a>
                                </li>

                                <li class="text-left list-style-type-none btn-sm btn-primary my-1">
                                    <a class="text-white" (click)="getAttachments(item)">Attachment
                                        <span>(</span><span>{{item.attachementCount}}</span><span>)</span></a>
                                </li>
                                <ng-container *ngIf="item.isPrimeFacility=='0'">
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white" (click)="openEditPatient(item)">Edit
                                            Patient</a>
                                    </li>
                                    <li
                                        class="text-left list-style-type-none btn-sm btn-primary my-1">
                                        <a class="text-white"
                                            (click)="CloseHospitalizationStatus(item)">
                                            Close Hospitalization</a>
                                    </li>
                                </ng-container>
                            </ng-container>
                        </ul>
                    </div>
                </ng-template>

            </ng-container>
            <div class="row mx-auto">
                <div class="col-12">
                    <pagination-controls previousLabel="" nextLabel=""
                        (pageChange)="getPatients($event)">
                    </pagination-controls>
                </div>
            </div>
        </div>
        <!-- mobile end-->
    </div>
</div>

<!---- App View History Start-->
<app-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
    [historyTotalCount]="historyTotalCount"></app-view-history>
<!---- App View History End-->

<!-- attachment popup starts -->
<app-upload-attachment (eventUpdateCount)="updateAttCount(patient)" [lisfOfAttachments]="lisfOfAttachments"
    [PatientObject]="patient"></app-upload-attachment>
<!-- attachment popup ends -->

<!-- send note Popup Starts -->
<app-send-note [PatientObject]='patient'
    [listOfUsersAndGroups]="listOfUsersAndGroups" [lisfOfSentNotes]="lisfOfSentNotes"
    [selectedUsersAndGroups]="selectedUsersAndGroups" (eventListOfNotes)="openNotes(patient)"></app-send-note>
<!-- send note Popup Ends -->

<!-- Assign Popup Starts -->
<app-assign-to-others-or-residents [patient]="patient" [listOfPhysicians]="listOfPhysicians"
    [listOfProvider]="listOfProvider" (eventListOfPatients)="getPatients(p)">
</app-assign-to-others-or-residents>
<!-- Assign Popup Ends -->

<!-- Edit Patient Popup Starts -->
<app-edit-patient [patient]="patient" (eventListOfPatients)="getPatients(p)" [userType]="'PHYSICIAN'"></app-edit-patient>
<!-- Edit Patient Popup Ends -->

<!-- Undo Dischare Confiramation Popup Model Starts -->
<app-undo-discharge-patient [patient]="patient"
    (eventListOfPatients)="getPatients(p)"></app-undo-discharge-patient>
<!-- Undo Dischare Confiramation Popup Model Ends -->